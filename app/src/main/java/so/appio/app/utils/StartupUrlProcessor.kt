package so.appio.app.utils

import android.util.Log
import kotlinx.coroutines.delay

private const val TAG = "LOG:StartupUrlProcessor"

data class URLParams(
    val serviceId: String,
    val showPreview: Boolean = false
)

/**
 * Centralized handler for all URL processing during app startup.
 * Determines whether to present ServiceScreen based on URL validation.
 *
 * Triggered by Intents, Install Referrer, and QR Scanner.
 */
object StartupUrlProcessor {

    /**
     * Processes a URL to determine the appropriate startup action.
     * 
     * @param data The URL string to process
     * @return URLParams|null
     */
    suspend fun processUrl(data: String): URLParams? {
        Log.d(TAG, "Processing URL: $data")
        
        if (data.isBlank()) {
            Log.d(TAG, "Empty URL provided, returning NoAction")
            return null
        }
        
        // Validate and parse the URL using existing UrlValidator
        return when (val result = UrlValidator.validateAndParseUrl(data)) {
            is UrlValidationResult.Success -> {
                Log.d(TAG, "Valid URL found - serviceId: ${result.params.serviceId}, customerUserId: ${result.params.customerUserId}")

                // TODO: register/get device
                // TODO: link service and device
                // TODO: get service data
                // Simulate async initialization logic with 1-second delay
                delay(2000)

                URLParams(serviceId = result.params.serviceId, showPreview =  false)
            }
            is UrlValidationResult.Error -> {
                Log.e(TAG, "Invalid URL: ${result.message}")
                // Return immediately for invalid URLs (no delay)
                return null
            }
        }
    }
}

package so.appio.app.network

import android.content.Context
import android.os.Build
import android.provider.Settings
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json
import kotlinx.serialization.SerializationException
import okhttp3.*
import so.appio.app.BuildConfig

object APIClient {
    const val TAG = "LOG:APIClient"
    const val BASE_URL = "https://api.appio.so/ios"

    @JvmStatic
    var appContext: Context? = null
        private set

    @JvmStatic
    val httpClient: OkHttpClient by lazy { HttpClientConfig.createClient() }

    fun initialize(context: Context) {
        appContext = context.applicationContext
    }

    suspend inline fun <reified T> performRequest(
        endpoint: String,
        method: String,
        headers: Map<String, String> = emptyMap(),
        body: String? = null,
        expectedStatusCode: Int = 200,
    ): T? {
        val context = appContext
            ?: throw IllegalStateException("APIClient not initialized. Call initialize() first.")

        val httpUrl = HttpUrl.parse("$BASE_URL$endpoint")
            ?: throw APIError.InvalidUrl
        val url = httpUrl.toString()

        val requestBody = if (body != null) {
            RequestBody.create(MediaType.parse("application/json"), body)
        } else null

        val requestBuilder = Request.Builder()
            .url(httpUrl)
            .method(method, requestBody)
            .addHeader("Authorization", "Bearer ${BuildConfig.API_AUTH_TOKEN}")
            .addHeader("Content-Type", "application/json")
            .addHeader("X-App-Platform", "android")
            .addHeader("X-App-Version", BuildConfig.VERSION_NAME)
            .addHeader("X-System-Version", Build.VERSION.RELEASE ?: "")
            .addHeader("X-Device-Identifier", Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID) ?: "")
            .addHeader("X-Screen-Size", getScreenSize(context))

        for ((key, value) in headers) {
            requestBuilder.addHeader(key, value)
        }

        val request = requestBuilder.build()

        return try {
            val response = withContext(Dispatchers.IO) { httpClient.newCall(request).execute() }
            val responseBody = response.body()?.string() ?: throw APIError.BadServerResponse

            Log.d(TAG, "---------- <API>")
            Log.d(TAG, "API method: $method")
            Log.d(TAG, "API request url: $url")
            Log.d(TAG, "API request headers: ${request.headers()}")
            Log.d(TAG, "API request body: $body")
            Log.d(TAG, "API response code: ${response.code()}")
            Log.d(TAG, "API response body: $responseBody")
            Log.d(TAG, "---------- </API>")

            if (response.code() == 404) {
                return null
            }

            if (response.code() != expectedStatusCode) {
                throw APIError.UnexpectedStatusCode(response.code(), responseBody)
            }

            return try {
                Json.decodeFromString(responseBody)
            } catch (e: SerializationException) {
                throw APIError.FailedToDecode(responseBody)
            }
        } catch (e: java.net.SocketTimeoutException) {
            throw APIError.NetworkTimeout
        } catch (e: APIError) {
            throw e
        } catch (e: Exception) {
            throw APIError.NetworkError(e)
        }
    }

    fun getScreenSize(context: Context): String {
        val metrics = context.resources.displayMetrics
        return "${metrics.widthPixels}x${metrics.heightPixels}"
    }
}
